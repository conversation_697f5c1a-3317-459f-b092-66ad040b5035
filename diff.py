import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import PatternFill

# 文件路径
file_1171 = "spelldbc1171.xlsx"  # 基准文件
file_1180 = "spelldbc1180.xlsx"  # 比较文件
output_file = "diff_resultspell.xlsx"

# 忽略这些字段（你提供的列表）
ignore_columns = {
    "SpellPriority","Name_enUS","Name_enGB","Name_koKR","Name_frFR","Name_deDE","Name_enCN","Name_zhCN","Name_enTW","Name_Mask",
    "NameSubtext_enUS","NameSubtext_enGB","NameSubtext_koKR","NameSubtext_frFR","NameSubtext_deDE","NameSubtext_enCN","NameSubtext_zhCN","NameSubtext_enTW","NameSubtext_Mask",
    "Description_enUS","Description_enGB","Description_koKR","Description_frFR","Description_deDE","Description_enCN","Description_zhCN","Description_enTW","Description_Mask",
    "AuraDescription_enUS","AuraDescription_enGB","AuraDescription_koKR","AuraDescription_frFR","AuraDescription_deDE","AuraDescription_enCN","AuraDescription_zhCN","AuraDescription_enTW","AuraDescription_Mask"
}

# 读取 Excel
df_base = pd.read_excel(file_1171)  # 基准文件 (1171)
df_new = pd.read_excel(file_1180)   # 新文件 (1180)

# 保证两个表字段完全一致
if not (df_base.columns == df_new.columns).all():
    raise ValueError("两个表的字段不一致，请检查 Excel 文件！")

# 假设第一列是ID列，用于匹配行
id_column = df_base.columns[0]

# 创建基于ID的字典，方便查找
base_dict = df_base.set_index(id_column).to_dict('index')
new_dict = df_new.set_index(id_column).to_dict('index')

# 找出有改动的行和新增的行
changed_rows = []
new_rows = []

# 检查新文件中的每一行
for new_id, new_row in new_dict.items():
    if new_id in base_dict:
        # 存在于基准文件中，检查是否有改动
        base_row = base_dict[new_id]
        has_changes = False

        for col in df_new.columns:
            if col in ignore_columns:
                continue

            base_val = base_row.get(col)
            new_val = new_row.get(col)

            # 比较值是否不同
            if pd.isna(base_val) and pd.isna(new_val):
                continue
            elif base_val != new_val:
                has_changes = True
                break

        if has_changes:
            # 添加到改动行列表
            row_data = {id_column: new_id}
            row_data.update(new_row)
            changed_rows.append(row_data)
    else:
        # 不存在于基准文件中，是新增的行
        row_data = {id_column: new_id}
        row_data.update(new_row)
        new_rows.append(row_data)

# 合并改动的行和新增的行
result_rows = changed_rows + new_rows

if result_rows:
    # 创建结果DataFrame
    df_result = pd.DataFrame(result_rows)

    # 确保列的顺序与原文件一致
    df_result = df_result[df_new.columns]

    # 保存到Excel
    df_result.to_excel(output_file, index=False)

    # 打开 Excel 用 openpyxl 加样式
    wb = load_workbook(output_file)
    ws = wb.active

    # 红色填充（用于标记改动的字段）
    red_fill = PatternFill(start_color="FFFF0000", end_color="FFFF0000", fill_type="solid")
    # 绿色填充（用于标记新增的行）
    green_fill = PatternFill(start_color="FF00FF00", end_color="FF00FF00", fill_type="solid")

    # 遍历结果中的每一行，标记改动
    for row_idx, (_, result_row) in enumerate(df_result.iterrows(), start=2):
        row_id = result_row[id_column]

        if row_id in base_dict:
            # 这是改动的行，标记改动的字段为红色
            base_row = base_dict[row_id]
            for col_idx, col in enumerate(df_result.columns, start=1):
                if col in ignore_columns:
                    continue

                base_val = base_row.get(col)
                new_val = result_row[col]

                if pd.isna(base_val) and pd.isna(new_val):
                    continue
                elif base_val != new_val:
                    ws.cell(row=row_idx, column=col_idx).fill = red_fill
        else:
            # 这是新增的行，整行标记为绿色
            for col_idx in range(1, len(df_result.columns) + 1):
                ws.cell(row=row_idx, column=col_idx).fill = green_fill

    # 保存最终结果
    wb.save(output_file)

    print(f"比较完成！")
    print(f"找到 {len(changed_rows)} 行有改动的数据")
    print(f"找到 {len(new_rows)} 行新增的数据")
    print(f"结果已保存到 {output_file}")
    print(f"红色标记：改动的字段")
    print(f"绿色标记：新增的行")
else:
    print("没有发现改动或新增的数据")
