import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import PatternFill

# 文件路径
file_1171 = "spelldbc1171.xlsx"  # 基准文件
file_1180 = "spelldbc1180.xlsx"  # 比较文件
output_file = "diff_resultspell.xlsx"

# 忽略这些字段（你提供的列表）
ignore_columns = {
    "SpellPriority","Name_enUS","Name_enGB","Name_koKR","Name_frFR","Name_deDE","Name_enCN","Name_zhCN","Name_enTW","Name_Mask",
    "NameSubtext_enUS","NameSubtext_enGB","NameSubtext_koKR","NameSubtext_frFR","NameSubtext_deDE","NameSubtext_enCN","NameSubtext_zhCN","NameSubtext_enTW","NameSubtext_Mask",
    "Description_enUS","Description_enGB","Description_koKR","Description_frFR","Description_deDE","Description_enCN","Description_zhCN","Description_enTW","Description_Mask",
    "AuraDescription_enUS","AuraDescription_enGB","AuraDescription_koKR","AuraDescription_frFR","AuraDescription_deDE","AuraDescription_enCN","AuraDescription_zhCN","AuraDescription_enTW","AuraDescription_Mask"
}

# 读取 Excel
df_base = pd.read_excel(file_1171)  # 基准文件 (1171)
df_new = pd.read_excel(file_1180)   # 新文件 (1180)

# 保证两个表字段完全一致
if not (df_base.columns == df_new.columns).all():
    raise ValueError("两个表的字段不一致，请检查 Excel 文件！")

# 假设第一列是ID列，用于匹配行
id_column = df_base.columns[0]

# 创建基于ID的字典，方便查找
base_dict = df_base.set_index(id_column).to_dict('index')
new_dict = df_new.set_index(id_column).to_dict('index')

# 找出有改动的行和新增的行
changed_ids = []
new_ids = []
changed_fields_dict = {}  # 存储每个ID的差异字段信息

# 检查新文件中的每一行
for new_id, new_row in new_dict.items():
    if new_id in base_dict:
        # 存在于基准文件中，检查是否有改动
        base_row = base_dict[new_id]
        changed_fields = []

        for col in df_new.columns:
            if col in ignore_columns:
                continue

            base_val = base_row.get(col)
            new_val = new_row.get(col)

            # 比较值是否不同
            if pd.isna(base_val) and pd.isna(new_val):
                continue
            elif base_val != new_val:
                changed_fields.append(col)

        if changed_fields:
            changed_ids.append(new_id)
            changed_fields_dict[new_id] = changed_fields
    else:
        # 不存在于基准文件中，是新增的行
        new_ids.append(new_id)

# 收集所有有差异的字段
all_changed_fields = set()
for fields in changed_fields_dict.values():
    all_changed_fields.update(fields)

# 创建结果列：ID列 + 行类型列 + 所有有差异的字段列
result_columns = [id_column, '行类型'] + sorted(list(all_changed_fields))

# 创建结果数据列表
result_rows = []

# 处理有改动的行（每个ID对应三行：字段名、1171版本、1180版本）
for changed_id in changed_ids:
    changed_fields = changed_fields_dict[changed_id]
    base_row = base_dict[changed_id]
    new_row = new_dict[changed_id]

    # 第一行：字段名称行
    field_names_row = {
        id_column: changed_id,
        '行类型': '字段名称'
    }
    for field in all_changed_fields:
        if field in changed_fields:
            field_names_row[field] = field  # 显示字段名称
        else:
            field_names_row[field] = ''  # 非差异字段留空
    result_rows.append(field_names_row)

    # 第二行：1171版本的值
    row_1171 = {
        id_column: changed_id,
        '行类型': '1171版本'
    }
    for field in all_changed_fields:
        if field in changed_fields:
            row_1171[field] = base_row.get(field, '')
        else:
            row_1171[field] = ''
    result_rows.append(row_1171)

    # 第三行：1180版本的值
    row_1180 = {
        id_column: changed_id,
        '行类型': '1180版本'
    }
    for field in all_changed_fields:
        if field in changed_fields:
            row_1180[field] = new_row.get(field, '')
        else:
            row_1180[field] = ''
    result_rows.append(row_1180)

# 处理新增的行（只有1180版本）
for new_id in new_ids:
    new_row = new_dict[new_id]

    # 新增行的字段名称行
    field_names_row = {
        id_column: new_id,
        '行类型': '新增-字段名'
    }
    for field in all_changed_fields:
        field_names_row[field] = field
    result_rows.append(field_names_row)

    # 新增行的值
    row_data = {
        id_column: new_id,
        '行类型': '新增-1180版本'
    }
    for field in all_changed_fields:
        row_data[field] = new_row.get(field, '')
    result_rows.append(row_data)

if result_rows:
    # 创建结果DataFrame
    df_result = pd.DataFrame(result_rows)

    # 确保列的顺序正确
    df_result = df_result[result_columns]

    # 保存到Excel
    df_result.to_excel(output_file, index=False)

    # 打开 Excel 用 openpyxl 加样式
    wb = load_workbook(output_file)
    ws = wb.active

    # 定义颜色填充
    # 蓝色填充（用于字段名称行）
    blue_fill = PatternFill(start_color="FF87CEEB", end_color="FF87CEEB", fill_type="solid")
    # 红色填充（用于1171版本行）
    red_fill = PatternFill(start_color="FFFF0000", end_color="FFFF0000", fill_type="solid")
    # 紫色填充（用于1180版本行）
    purple_fill = PatternFill(start_color="FF800080", end_color="FF800080", fill_type="solid")
    # 绿色填充（用于新增行）
    green_fill = PatternFill(start_color="FF00FF00", end_color="FF00FF00", fill_type="solid")
    # 浅灰色填充（用于行类型列）
    gray_fill = PatternFill(start_color="FFD3D3D3", end_color="FFD3D3D3", fill_type="solid")

    # 遍历结果中的每一行，标记颜色
    row_idx = 2  # 从第2行开始（第1行是标题）

    for _, row_data in df_result.iterrows():
        row_type = row_data['行类型']
        row_id = row_data[id_column]

        # 标记行类型列为灰色
        type_col_idx = result_columns.index('行类型') + 1
        ws.cell(row=row_idx, column=type_col_idx).fill = gray_fill

        if row_type == '字段名称':
            # 字段名称行，有内容的字段标记为蓝色
            if row_id in changed_fields_dict:
                changed_fields = changed_fields_dict[row_id]
                for field in changed_fields:
                    if field in result_columns:
                        col_idx = result_columns.index(field) + 1
                        ws.cell(row=row_idx, column=col_idx).fill = blue_fill

        elif row_type == '1171版本':
            # 1171版本行，有内容的字段标记为红色
            if row_id in changed_fields_dict:
                changed_fields = changed_fields_dict[row_id]
                for field in changed_fields:
                    if field in result_columns:
                        col_idx = result_columns.index(field) + 1
                        ws.cell(row=row_idx, column=col_idx).fill = red_fill

        elif row_type == '1180版本':
            # 1180版本行，有内容的字段标记为紫色
            if row_id in changed_fields_dict:
                changed_fields = changed_fields_dict[row_id]
                for field in changed_fields:
                    if field in result_columns:
                        col_idx = result_columns.index(field) + 1
                        ws.cell(row=row_idx, column=col_idx).fill = purple_fill

        elif row_type in ['新增-字段名', '新增-1180版本']:
            # 新增行，所有字段标记为绿色
            for col_idx in range(3, len(result_columns) + 1):  # 从第3列开始（跳过ID和行类型列）
                ws.cell(row=row_idx, column=col_idx).fill = green_fill

        row_idx += 1

    # 调整列宽以便更好地显示内容
    for col_idx, col in enumerate(result_columns, start=1):
        if col == '行类型':
            ws.column_dimensions[ws.cell(row=1, column=col_idx).column_letter].width = 12
        elif col == id_column:
            ws.column_dimensions[ws.cell(row=1, column=col_idx).column_letter].width = 10
        else:
            ws.column_dimensions[ws.cell(row=1, column=col_idx).column_letter].width = 15

    # 保存最终结果
    wb.save(output_file)

    print(f"比较完成！")
    print(f"找到 {len(changed_ids)} 个ID有改动的数据（每个ID显示为3行组合）")
    print(f"找到 {len(new_ids)} 行新增的数据（每个ID显示为2行组合）")
    print(f"共有 {len(all_changed_fields)} 个字段存在差异")
    print(f"差异字段列表: {', '.join(sorted(all_changed_fields))}")
    print(f"结果已保存到 {output_file}")
    print(f"\n显示格式说明：")
    print(f"每个有差异的ID显示为3行组合：")
    print(f"  第1行：字段名称（蓝色标记）")
    print(f"  第2行：1171版本值（红色标记）")
    print(f"  第3行：1180版本值（紫色标记）")
    print(f"新增的ID显示为2行组合：")
    print(f"  第1行：字段名称（绿色标记）")
    print(f"  第2行：1180版本值（绿色标记）")
else:
    print("没有发现改动或新增的数据")
