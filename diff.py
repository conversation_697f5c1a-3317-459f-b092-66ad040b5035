import pandas as pd
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill

# 文件路径
file_1171 = "spelldbc1171.xlsx"  # 基准文件
file_1180 = "spelldbc1180.xlsx"  # 比较文件
output_file = "diff_resultspell.xlsx"

# 忽略这些字段（但保留Name_deDE和Description_deDE用于固定显示）
ignore_columns = {
    "SpellPriority","Name_enUS","Name_enGB","Name_koKR","Name_frFR","Name_enCN","Name_zhCN","Name_enTW","Name_Mask",
    "NameSubtext_enUS","NameSubtext_enGB","NameSubtext_koKR","NameSubtext_frFR","NameSubtext_deDE","NameSubtext_enCN","NameSubtext_zhCN","NameSubtext_enTW","NameSubtext_Mask",
    "Description_enUS","Description_enGB","Description_koKR","Description_frFR","Description_enCN","Description_zhCN","Description_enTW","Description_Mask",
    "AuraDescription_enUS","AuraDescription_enGB","AuraDescription_koKR","AuraDescription_frFR","AuraDescription_deDE","AuraDescription_enCN","AuraDescription_zhCN","AuraDescription_enTW","AuraDescription_Mask"
}

# 读取 Excel
df_base = pd.read_excel(file_1171)  # 基准文件 (1171)
df_new = pd.read_excel(file_1180)   # 新文件 (1180)

# 保证两个表字段完全一致
if not (df_base.columns == df_new.columns).all():
    raise ValueError("两个表的字段不一致，请检查 Excel 文件！")

# 假设第一列是ID列，用于匹配行
id_column = df_base.columns[0]

# 创建基于ID的字典，方便查找
base_dict = df_base.set_index(id_column).to_dict('index')
new_dict = df_new.set_index(id_column).to_dict('index')

# 找出有改动的行和新增的行
changed_ids = []
new_ids = []
changed_fields_dict = {}  # 存储每个ID的差异字段信息

# 检查新文件中的每一行
for new_id, new_row in new_dict.items():
    if new_id in base_dict:
        # 存在于基准文件中，检查是否有改动
        base_row = base_dict[new_id]
        changed_fields = []

        for col in df_new.columns:
            if col in ignore_columns:
                continue

            base_val = base_row.get(col)
            new_val = new_row.get(col)

            # 比较值是否不同
            if pd.isna(base_val) and pd.isna(new_val):
                continue
            elif base_val != new_val:
                changed_fields.append(col)

        if changed_fields:
            changed_ids.append(new_id)
            changed_fields_dict[new_id] = changed_fields
    else:
        # 不存在于基准文件中，是新增的行
        new_ids.append(new_id)

# 创建一个手动构建的Excel文件，每个ID组合独立显示其差异字段
wb = Workbook()
ws = wb.active

# 定义颜色填充
blue_fill = PatternFill(start_color="FF87CEEB", end_color="FF87CEEB", fill_type="solid")
red_fill = PatternFill(start_color="FFFF0000", end_color="FFFF0000", fill_type="solid")
purple_fill = PatternFill(start_color="FF800080", end_color="FF800080", fill_type="solid")
green_fill = PatternFill(start_color="FF00FF00", end_color="FF00FF00", fill_type="solid")
gray_fill = PatternFill(start_color="FFD3D3D3", end_color="FFD3D3D3", fill_type="solid")

current_row = 1

# 处理有改动的行 - 固定列结构
for changed_id in changed_ids:
    changed_fields = changed_fields_dict[changed_id]
    base_row = base_dict[changed_id]
    new_row = new_dict[changed_id]

    # 获取除了固定字段外的其他差异字段
    other_changed_fields = []
    for field in sorted(changed_fields):
        if field not in ['Name_deDE', 'Description_deDE']:
            other_changed_fields.append(field)

    # 第一行：字段名称行
    current_col = 1
    ws.cell(row=current_row, column=current_col, value=changed_id)
    current_col += 1
    ws.cell(row=current_row, column=current_col, value='字段名称').fill = gray_fill
    current_col += 1

    # 固定第3列：Name_deDE
    if 'Name_deDE' in changed_fields:
        ws.cell(row=current_row, column=current_col, value='Name_deDE').fill = blue_fill
    else:
        ws.cell(row=current_row, column=current_col, value='Name_deDE')
    current_col += 1

    # 固定第4列：Description_deDE
    if 'Description_deDE' in changed_fields:
        ws.cell(row=current_row, column=current_col, value='Description_deDE').fill = blue_fill
    else:
        ws.cell(row=current_row, column=current_col, value='Description_deDE')
    current_col += 1

    # 其他差异字段
    for field in other_changed_fields:
        ws.cell(row=current_row, column=current_col, value=field).fill = blue_fill
        current_col += 1
    current_row += 1

    # 第二行：1171版本的值
    current_col = 1
    ws.cell(row=current_row, column=current_col, value=changed_id)
    current_col += 1
    ws.cell(row=current_row, column=current_col, value='1171版本').fill = gray_fill
    current_col += 1

    # 固定第3列：Name_deDE的值
    name_value = base_row.get('Name_deDE', '')
    if 'Name_deDE' in changed_fields:
        ws.cell(row=current_row, column=current_col, value=name_value).fill = red_fill
    else:
        ws.cell(row=current_row, column=current_col, value=name_value)
    current_col += 1

    # 固定第4列：Description_deDE的值
    desc_value = base_row.get('Description_deDE', '')
    if 'Description_deDE' in changed_fields:
        ws.cell(row=current_row, column=current_col, value=desc_value).fill = red_fill
    else:
        ws.cell(row=current_row, column=current_col, value=desc_value)
    current_col += 1

    # 其他差异字段的值
    for field in other_changed_fields:
        ws.cell(row=current_row, column=current_col, value=base_row.get(field, '')).fill = red_fill
        current_col += 1
    current_row += 1

    # 第三行：1180版本的值
    current_col = 1
    ws.cell(row=current_row, column=current_col, value=changed_id)
    current_col += 1
    ws.cell(row=current_row, column=current_col, value='1180版本').fill = gray_fill
    current_col += 1

    # 固定第3列：Name_deDE的值
    name_value = new_row.get('Name_deDE', '')
    if 'Name_deDE' in changed_fields:
        ws.cell(row=current_row, column=current_col, value=name_value).fill = purple_fill
    else:
        ws.cell(row=current_row, column=current_col, value=name_value)
    current_col += 1

    # 固定第4列：Description_deDE的值
    desc_value = new_row.get('Description_deDE', '')
    if 'Description_deDE' in changed_fields:
        ws.cell(row=current_row, column=current_col, value=desc_value).fill = purple_fill
    else:
        ws.cell(row=current_row, column=current_col, value=desc_value)
    current_col += 1

    # 其他差异字段的值
    for field in other_changed_fields:
        ws.cell(row=current_row, column=current_col, value=new_row.get(field, '')).fill = purple_fill
        current_col += 1
    current_row += 1

# 处理新增的行 - 固定列结构
for new_id in new_ids:
    new_row = new_dict[new_id]

    # 获取除了固定字段外的其他非忽略字段
    other_new_fields = []
    for col in df_new.columns:
        if col not in ignore_columns and col != id_column and col not in ['Name_deDE', 'Description_deDE']:
            other_new_fields.append(col)

    sorted_other_fields = sorted(other_new_fields)

    # 新增行的字段名称行
    current_col = 1
    ws.cell(row=current_row, column=current_col, value=new_id)
    current_col += 1
    ws.cell(row=current_row, column=current_col, value='新增-字段名').fill = gray_fill
    current_col += 1

    # 固定第3列：Name_deDE
    ws.cell(row=current_row, column=current_col, value='Name_deDE').fill = green_fill
    current_col += 1

    # 固定第4列：Description_deDE
    ws.cell(row=current_row, column=current_col, value='Description_deDE').fill = green_fill
    current_col += 1

    # 其他字段
    for field in sorted_other_fields:
        ws.cell(row=current_row, column=current_col, value=field).fill = green_fill
        current_col += 1
    current_row += 1

    # 新增行的值
    current_col = 1
    ws.cell(row=current_row, column=current_col, value=new_id)
    current_col += 1
    ws.cell(row=current_row, column=current_col, value='新增-1180版本').fill = gray_fill
    current_col += 1

    # 固定第3列：Name_deDE的值
    ws.cell(row=current_row, column=current_col, value=new_row.get('Name_deDE', '')).fill = green_fill
    current_col += 1

    # 固定第4列：Description_deDE的值
    ws.cell(row=current_row, column=current_col, value=new_row.get('Description_deDE', '')).fill = green_fill
    current_col += 1

    # 其他字段的值
    for field in sorted_other_fields:
        ws.cell(row=current_row, column=current_col, value=new_row.get(field, '')).fill = green_fill
        current_col += 1
    current_row += 1

# 动态调整列宽 - 根据内容自动调整
for col in ws.columns:
    max_length = 0
    column = col[0].column_letter
    for cell in col:
        try:
            if len(str(cell.value)) > max_length:
                max_length = len(str(cell.value))
        except:
            pass
    adjusted_width = min(max_length + 2, 20)  # 最大宽度限制为20
    ws.column_dimensions[column].width = adjusted_width

# 保存文件
wb.save(output_file)

# 收集统计信息
all_changed_fields = set()
for fields in changed_fields_dict.values():
    all_changed_fields.update(fields)

print(f"比较完成！")
print(f"找到 {len(changed_ids)} 个ID有改动的数据（每个ID显示为3行组合）")
print(f"找到 {len(new_ids)} 行新增的数据（每个ID显示为2行组合）")
print(f"共有 {len(all_changed_fields)} 个字段存在差异")
print(f"差异字段列表: {', '.join(sorted(all_changed_fields))}")
print(f"结果已保存到 {output_file}")
print(f"\n显示格式说明：")
print(f"✅ 固定列结构：第1列=ID，第2列=行类型，第3列=Name_deDE，第4列=Description_deDE")
print(f"✅ 第5列及以后：动态显示其他差异字段")
print(f"✅ Name_deDE和Description_deDE始终显示，有差异时着色")
print(f"✅ 动态列宽，根据内容自动调整")
print(f"每个有差异的ID显示为3行组合：")
print(f"  第1行：字段名称（蓝色标记差异字段）")
print(f"  第2行：1171版本值（红色标记差异字段）")
print(f"  第3行：1180版本值（紫色标记差异字段）")
print(f"新增的ID显示为2行组合：")
print(f"  第1行：字段名称（绿色标记）")
print(f"  第2行：1180版本值（绿色标记）")

if not changed_ids and not new_ids:
    print("没有发现改动或新增的数据")
