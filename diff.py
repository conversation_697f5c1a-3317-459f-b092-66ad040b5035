import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import PatternFill

# 文件路径
file_1171 = "spelldbc1171.xlsx"
file_1180 = "spelldbc1180.xlsx"
output_file = "diff_resultspell.xlsx"

# 忽略这些字段（你提供的列表）
ignore_columns = {
    "SpellPriority","Name_enUS","Name_enGB","Name_koKR","Name_frFR","Name_deDE","Name_enCN","Name_zhCN","Name_enTW","Name_Mask",
    "NameSubtext_enUS","NameSubtext_enGB","NameSubtext_koKR","NameSubtext_frFR","NameSubtext_deDE","NameSubtext_enCN","NameSubtext_zhCN","NameSubtext_enTW","NameSubtext_Mask",
    "Description_enUS","Description_enGB","Description_koKR","Description_frFR","Description_deDE","Description_enCN","Description_zhCN","Description_enTW","Description_Mask",
    "AuraDescription_enUS","AuraDescription_enGB","AuraDescription_koKR","AuraDescription_frFR","AuraDescription_deDE","AuraDescription_enCN","AuraDescription_zhCN","AuraDescription_enTW","AuraDescription_Mask"
}

# 读取 Excel
df1 = pd.read_excel(file_1171)
df2 = pd.read_excel(file_1180)

# 保证两个表字段完全一致
if not (df1.columns == df2.columns).all():
    raise ValueError("两个表的字段不一致，请检查 Excel 文件！")

# 初步保存（内容先写入）
df1.to_excel(output_file, index=False)

# 打开 Excel 用 openpyxl 加样式
wb = load_workbook(output_file)
ws = wb.active

# 红色填充
red_fill = PatternFill(start_color="FFFF0000", end_color="FFFF0000", fill_type="solid")

# 遍历单元格，对比 df1 和 df2 的值（跳过 ignore_columns）
for row in range(2, len(df1) + 2):  # +2 因为第一行是标题
    for col_idx, col in enumerate(df1.columns, start=1):
        if col in ignore_columns:  # 跳过这些字段
            continue
        val1 = df1.at[row - 2, col]
        val2 = df2.at[row - 2, col]
        if pd.isna(val1) and pd.isna(val2):
            continue
        if val1 != val2:  # 如果不同就标红
            ws.cell(row=row, column=col_idx).fill = red_fill

# 保存最终结果
wb.save(output_file)

print(f"比较完成！结果已保存到 {output_file}")
